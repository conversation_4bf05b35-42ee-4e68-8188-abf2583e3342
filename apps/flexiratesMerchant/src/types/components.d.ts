/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Avatar: typeof import('primevue/avatar')['default']
    BaseCardType: typeof import('./../components/common/BaseCardType.vue')['default']
    BaseDataTable: typeof import('./../components/common/BaseDataTable.vue')['default']
    BaseDataTableActions: typeof import('./../components/common/BaseDataTableActions.vue')['default']
    BaseDataTableSearch: typeof import('./../components/common/BaseDataTableSearch.vue')['default']
    BaseDataTableSearchItem: typeof import('./../components/common/BaseDataTableSearchItem.vue')['default']
    BaseDataTableSuperSearch: typeof import('./../components/common/BaseDataTableSuperSearch.vue')['default']
    BaseExportDialog: typeof import('./../components/common/BaseExportDialog.vue')['default']
    BaseFileUpload: typeof import('./../components/common/BaseFileUpload.vue')['default']
    BaseLoadingWrap: typeof import('./../components/common/BaseLoadingWrap.vue')['default']
    BasePopover: typeof import('./../components/common/BasePopover.vue')['default']
    BaseRangeDatePicker: typeof import('./../components/common/BaseRangeDatePicker.vue')['default']
    BaseSearch: typeof import('./../components/common/BaseSearch.vue')['default']
    BaseSearchItem: typeof import('./../components/common/BaseSearchItem.vue')['default']
    BaseTag: typeof import('./../components/common/BaseTag.vue')['default']
    Button: typeof import('primevue/button')['default']
    Checkbox: typeof import('primevue/checkbox')['default']
    CollapsePanel: typeof import('./../components/collapsePanel.vue')['default']
    Column: typeof import('primevue/column')['default']
    ColumnGroup: typeof import('primevue/columngroup')['default']
    ConfirmDialog: typeof import('primevue/confirmdialog')['default']
    CookiesPop: typeof import('./../components/cookiesPop/index.vue')['default']
    CustomDialog: typeof import('./../components/customDialog/index.vue')['default']
    CustomMessage: typeof import('./../components/customMessage/index.vue')['default']
    CustomPop: typeof import('./../components/customPop/index.vue')['default']
    DataTable: typeof import('primevue/datatable')['default']
    DatePicker: typeof import('primevue/datepicker')['default']
    Dialog: typeof import('primevue/dialog')['default']
    Divide: typeof import('./../components/divide/index.vue')['default']
    Divider: typeof import('primevue/divider')['default']
    Drawer: typeof import('primevue/drawer')['default']
    EnhancedDatePicker: typeof import('./../components/common/EnhancedDatePicker.vue')['default']
    Form: typeof import('./../components/Form/Form.vue')['default']
    FormField: typeof import('@primevue/forms/formfield')['default']
    FormItem: typeof import('./../components/Form/FormItem.vue')['default']
    GlobalSetup: typeof import('./../components/globalSetup/index.vue')['default']
    GoogleRecaptchaV2: typeof import('./../components/googleRecaptchaV2/index.vue')['default']
    Image: typeof import('primevue/image')['default']
    InputMask: typeof import('primevue/inputmask')['default']
    InputNumber: typeof import('primevue/inputnumber')['default']
    InputNumberRange: typeof import('./../components/InputNumberRange/index.vue')['default']
    InputRange: typeof import('./../components/InputRange/index.vue')['default']
    InputText: typeof import('primevue/inputtext')['default']
    MAutoComplete: typeof import('./../components/ui/MAutoComplete/index.vue')['default']
    MCascadeSelect: typeof import('./../components/ui/MCascadeSelect/index.vue')['default']
    MCheckbox: typeof import('./../components/ui/MCheckbox/index.vue')['default']
    MColorPicker: typeof import('./../components/ui/MColorPicker/index.vue')['default']
    MDatePicker: typeof import('./../components/ui/MDatePicker/index.vue')['default']
    MEditor: typeof import('./../components/ui/MEditor/index.vue')['default']
    Menu: typeof import('primevue/menu')['default']
    Message: typeof import('primevue/message')['default']
    MFloatLabel: typeof import('./../components/ui/MFloatLabel/index.vue')['default']
    MForm: typeof import('./../components/ui/MForm/index.vue')['default']
    MFormItem: typeof import('./../components/ui/MFormItem/index.vue')['default']
    MIconField: typeof import('./../components/ui/MIconField/index.vue')['default']
    MIftaLabel: typeof import('./../components/ui/MIftaLabel/index.vue')['default']
    MInputGroup: typeof import('./../components/ui/MInputGroup/index.vue')['default']
    MInputMask: typeof import('./../components/ui/MInputMask/index.vue')['default']
    MInputNumber: typeof import('./../components/ui/MInputNumber/index.vue')['default']
    MInputotp: typeof import('./../components/ui/MInputotp/index.vue')['default']
    MInputText: typeof import('./../components/ui/MInputText/index.vue')['default']
    MKnob: typeof import('./../components/ui/MKnob/index.vue')['default']
    MListbox: typeof import('./../components/ui/MListbox/index.vue')['default']
    MMultiSelect: typeof import('./../components/ui/MMultiSelect/index.vue')['default']
    MPassword: typeof import('./../components/ui/MPassword/index.vue')['default']
    MRadioButton: typeof import('./../components/ui/MRadioButton/index.vue')['default']
    MRating: typeof import('./../components/ui/MRating/index.vue')['default']
    MSelect: typeof import('./../components/ui/MSelect/index.vue')['default']
    MSelectButton: typeof import('./../components/ui/MSelectButton/index.vue')['default']
    MSlider: typeof import('./../components/ui/MSlider/index.vue')['default']
    MTextarea: typeof import('./../components/ui/MTextarea/index.vue')['default']
    MToggleButton: typeof import('./../components/ui/MToggleButton/index.vue')['default']
    MToggleSwitch: typeof import('./../components/ui/MToggleSwitch/index.vue')['default']
    MTreeSelect: typeof import('./../components/ui/MTreeSelect/index.vue')['default']
    MultiSelect: typeof import('primevue/multiselect')['default']
    OverlayBadge: typeof import('primevue/overlaybadge')['default']
    Password: typeof import('primevue/password')['default']
    PermissionWrapper: typeof import('./../components/Permission/PermissionWrapper.vue')['default']
    Popover: typeof import('primevue/popover')['default']
    ProgressSpinner: typeof import('primevue/progressspinner')['default']
    RadioButton: typeof import('primevue/radiobutton')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Row: typeof import('primevue/row')['default']
    ScheduleEditDialog: typeof import('./../components/scheduleDialog/ScheduleEditDialog.vue')['default']
    ScrollPanel: typeof import('primevue/scrollpanel')['default']
    Select: typeof import('primevue/select')['default']
    Tag: typeof import('primevue/tag')['default']
    Textarea: typeof import('primevue/textarea')['default']
    TieredMenu: typeof import('primevue/tieredmenu')['default']
    Timeline: typeof import('primevue/timeline')['default']
    Toast: typeof import('primevue/toast')['default']
    Tree: typeof import('primevue/tree')['default']
  }
}
