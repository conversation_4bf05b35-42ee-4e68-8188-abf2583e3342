<script setup lang="ts">
import Skeleton from 'primevue/skeleton'
import { useToast } from 'primevue/usetoast'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { getCustomerConfig } from '@/services/api/customer'
import { updateNotification } from '@/services/api/notification'
import Edit from './component/edit.vue'
import emailPreview from './component/emailPreview.vue'

const router = useRouter()

const headlineColor = ref('#fe4c1c')
const previewLogo = ref<any>([])
const template = ref('')
const toast = useToast()
const { t } = useI18n()
const isLoading = ref(true)

const updateSelectedColor = (color: string) => {
  headlineColor.value = color
}

const updateContent = (content: string) => {
  template.value = content
}
const updateNotificationType = async (type: number) => {
  isLoading.value = true
  try {
    const sendData = {
      notification_type: type,
      content: null,
      logo: previewLogo.value[0].url,
      theme: headlineColor.value,
    }
    const res = await updateNotification(sendData)
    if (res.code === 0) {
      toast.add({
        severity: 'success',
        summary: t('common.success', 'Success'),
        detail: 'Successfully updated',
        life: 3000,
      })
      router.push({ name: 'customersList' })
    }
  }
  catch (error: any) {
    console.error('Update notification error:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error', 'Error'),
      detail: t('common.operationFailed', 'Operation failed'),
      life: 3000,
    })
  }
  finally {
    isLoading.value = false
  }
}

const fetchConfig = async () => {
  isLoading.value = true
  try {
    const res = await getCustomerConfig()
    if (res.code === 0) {
      previewLogo.value = [{ url: res.data.logo }]
      headlineColor.value = res.data.theme
    }
  }
  catch (error: any) {
    console.error('Fetch config error:', error)
    toast.add({
      severity: 'error',
      summary: t('common.error', 'Error'),
      detail: t('common.fetchFailed', 'Failed to fetch data'),
      life: 3000,
    })
  }
  finally {
    isLoading.value = false
  }
}

fetchConfig()
</script>

<template>
  <div class="notification">
    <div class="notification-container flex justify-between items-start gap-4">
      <!-- Left side - Edit section -->
      <div class="notification-edit w-3/5">
        <!-- Skeleton loading for edit section -->
        <template v-if="isLoading">
          <div class="title-container mb-6 pb-4 border-[#d8d8d8] border-b-2">
            <Skeleton class="mb-2" height="40px" width="80%" />
            <Skeleton class="mb-2" height="20px" width="100%" />
            <Skeleton class="mb-2" height="20px" width="90%" />
          </div>
          <div class="edit-skeleton-container">
            <Skeleton class="mb-4" height="60px" width="100%" />
            <Skeleton class="mb-4" height="50px" width="40%" />
            <Skeleton class="mb-6" height="100px" width="100%" />
            <Skeleton class="mb-4" height="200px" width="100%" />
            <Skeleton class="mb-4" height="50px" width="30%" border-radius="16px" />
          </div>
        </template>

        <!-- Actual edit content -->
        <template v-else>
          <div class="title-container mb-6 pb-4 border-[#d8d8d8] border-b-2">
            <div class="title text-[26.6px] font-extrabold mb-2">
              Brand Your Experience
            </div>
            <div class="subtitle text-[#545454]">
              Create a professional payment experience - customise how your payment pages and email notifications will
              appear to customers.
            </div>
          </div>
          <div>
            <Edit
              v-model:logo="previewLogo"
              @update:selected-color="updateSelectedColor"
              @update:change-content="updateContent"
              @update:change-template-type="updateNotificationType"
            />
          </div>
        </template>
      </div>

      <!-- Right side - Preview section -->
      <div class="notification-preview w-2/5">
        <!-- Skeleton loading for preview section -->
        <template v-if="isLoading">
          <Skeleton class="mb-4" height="40px" width="50%" />
          <div class="preview-skeleton">
            <Skeleton class="mb-4" height="60px" width="100%" />
            <Skeleton class="mb-4" height="250px" width="100%" border-radius="8px" />
          </div>
        </template>

        <!-- Actual preview content -->
        <template v-else>
          <div class="text-[26.6px] font-extrabold mb-2">
            Preview
          </div>
          <emailPreview
            :header-color="headlineColor"
            :logo-src="previewLogo"
            :content="template"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.notification-edit,
.notification-preview {
  background-color: var(--color-white);
  border-radius: 16px;
  padding: 24px;
}

.edit-skeleton-container,
.preview-skeleton {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  :deep(.p-skeleton) {
    background-color: var(--surface-200);

    &::after {
      background: linear-gradient(90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0) 100%);
    }
  }
}
</style>
