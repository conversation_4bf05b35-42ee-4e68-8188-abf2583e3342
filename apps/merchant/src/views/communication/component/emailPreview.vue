<script setup lang="ts">
import defaultLogo from '@/assets/logo.png'

const props = defineProps({
  headerColor: {
    type: String,
    default: '#fe4c1c',
  },
  logoSrc: {
    type: Array,
    default: () => [],
  },
  content: {
    type: String,
    default: '<p style=\"font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;\">\r\n    Hi [Customer Name],\r\n<\/p>\r\n<p style=\"font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;\">\r\n    We\u2019re excited to introduce you to a simple way to view your subscription and payment details.\r\n<\/p>\r\n<p style=\"font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;\">\r\n    You can now securely access your <strong>subscription information<\/strong> and <strong>payment options<\/strong> directly through the link below \u2014 no login required.\r\n<\/p>\r\n<p style=\"font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;\">\r\n    <strong>What you can do through the link:<\/strong>\r\n<\/p>\r\n<ul class=\"ak-ul\" style=\"color:#292A2E;font-family:&quot;font-size:16px;background-color:#FFFFFF;\">\r\n    <li>\r\n        <p style=\"font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;\">\r\n            View your subscription plan and billing history\r\n        <\/p>\r\n    <\/li>\r\n    <li>\r\n        <p style=\"font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;\">\r\n            See upcoming payment dates and amounts\r\n        <\/p>\r\n    <\/li>\r\n    <li>\r\n        <p style=\"font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;\">\r\n            Make secure payments if needed\r\n        <\/p>\r\n    <\/li>\r\n<\/ul>\r\n<p style=\"font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;\">\r\n    If you notice any incorrect details or need to make changes to your subscription, please contact us directly.\r\n<\/p>\r\n<p style=\"font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;\">\r\n    <strong>Access your subscription and payment page here:<\/strong><br \/>\r\n    \uD83D\uDC49 <a href=\"[Jump Link]\" class=\"cc-1sdo59k\">Access Your Subscription &amp; Payments<\/a>\r\n<\/p>\r\n<hr \/>\r\n<p style=\"font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;\">\r\n    <strong>Security Information<\/strong><br \/>\r\n    Your privacy and security are important to us.<br \/>\r\n    The link provided is unique to your account and protected with industry-standard encryption.<br \/>\r\n    We recommend accessing it from a trusted device.\r\n<\/p>\r\n<hr \/>\r\n<p style=\"font-size:16px;font-family:ui-sans-serif, -apple-system, BlinkMacSystemFont, &quot;color:#292A2E;background-color:#FFFFFF;\">\r\n    <strong>Support Information<\/strong><br \/>\r\n    If you have any questions or need help, please contact the business directly using the details they have provided.<br \/>\r\n    We\u2019re here to assist you if needed.\r\n<\/p>\r\n',
  },
})

const getUrl = (logo: any[]) => {
  return logo[0]?.url || defaultLogo
}
</script>

<template>
  <div>
    <div class="email-container mt-4">
      <div class="email-preview bg-white rounded-lg overflow-hidden shadow-md">
        <!-- Browser Address Bar -->
        <div class="bg-gray-900 py-2 px-3 flex items-center">
          <div class="flex items-center space-x-2">
            <div class="flex space-x-1">
              <div class="w-2.5 h-2.5 rounded-full bg-red-500" />
              <div class="w-2.5 h-2.5 rounded-full bg-yellow-500" />
              <div class="w-2.5 h-2.5 rounded-full bg-green-500" />
            </div>
          </div>
          <div class="ml-4 bg-gray-800 rounded-full text-gray-300 text-xs py-1 px-3 flex items-center">
            <i class="pi pi-lock text-green-400 mr-1" />
            <span>https://email.com</span>
          </div>
        </div>

        <!-- Email Content -->
        <div class="flex flex-col items-center">
          <!-- Logo部分 - 白色背景 -->
          <div class="w-full bg-white p-6 flex justify-center">
            <div class="mb-4 cursor-pointer">
              <img
                :src="getUrl(props.logoSrc)" alt="Company Logo" class="w-24 h-24 object-contain"
                onerror="this.src='data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Crect%20x%3D%220%22%20y%3D%220%22%20width%3D%2220%22%20height%3D%2220%22%20fill%3D%22%23e2e8f0%22%2F%3E%3C%2Fsvg%3E'"
              >
            </div>
          </div>

          <!-- 内容部分 - 灰色背景 -->
          <div class="w-full py-4 flex flex-col items-center">
            <!-- 辅助灰色线条 -->
            <!-- <div class="flex flex-col items-center space-y-2 mb-6 w-full max-w-md">
              <div class="h-2 w-full bg-gray-300 rounded" />
              <div class="h-2 w-3/5 bg-gray-300 rounded" />
            </div> -->
            <div class="headline w-full h-12 font-bold text-2xl text-white flex items-center pl-8 mb-8" :style="{ 'background-color': `${props.headerColor}` }">
              {{ 'Your Headline Here' }}
            </div>

            <div class="w-full px-8 pb-8">
              <p v-html="props.content" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
