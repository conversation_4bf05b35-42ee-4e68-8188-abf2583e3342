<script setup lang="ts">
import type { PropType } from 'vue'
import { upload as uploadApi } from '@/services/api/downloadCenter'
import Badge from 'primevue/badge'
import <PERSON><PERSON> from 'primevue/button'
import Message from 'primevue/message'
import ProgressBar from 'primevue/progressbar'
import { computed, onBeforeUnmount, ref, watch } from 'vue'

// Define Props
const props = defineProps({
  /**
   * Upload mode: 'normal' - Default mode, 'avatar' - Avatar mode, 'gallery' - Gallery mode
   */
  mode: {
    type: String,
    default: 'normal',
    validator: (value: string) => ['normal', 'avatar', 'gallery', 'logo'].includes(value),
  },
  /**
   * Allow multiple file uploads
   */
  multiple: {
    type: Boolean,
    default: true,
  },
  /**
   * Accepted file types
   */
  accept: {
    type: String,
    default: '',
  },
  /**
   * Maximum file size (bytes)
   */
  maxSize: {
    type: Number,
    default: 5 * 1024 * 1024, // Default 5MB
  },
  /**
   * Maximum number of files
   */
  maxFiles: {
    type: Number,
    default: 0, // 0 means unlimited
  },
  /**
   * Auto upload files
   */
  autoUpload: {
    type: Boolean,
    default: true,
  },
  /**
   * Upload function (takes precedence over uploadUrl)
   * @example
   * ```
   * const myUploadFunction = async (file, options) => {
   *   // Custom upload logic
   *   if (options?.onProgress) {
   *     options.onProgress(50);
   *     // later
   *     options.onProgress(100);
   *   }
   *   return { success: true, fileId: 'xyz' };
   * }
   * ```
   */
  uploadFunction: {
    type: [Function, null] as unknown as PropType<FileUpload.UploadFunction | null>,
    default: null,
  },
  /**
   * Upload URL
   */
  uploadUrl: {
    type: String,
    default: '',
  },
  /**
   * Request headers
   */
  headers: {
    type: Object,
    default: () => ({}),
  },
  /**
   * Disable the uploader
   */
  disabled: {
    type: Boolean,
    default: false,
  },
  /**
   * Show file list
   */
  showFileList: {
    type: Boolean,
    default: true,
  },
  /**
   * Maximum height of file list
   */
  fileListMaxHeight: {
    type: String,
    default: '300px',
  },
  /**
   * Image compression options
   */
  imageCompressionOptions: {
    type: Object,
    default: () => ({
      enabled: false,
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 0.8,
      mimeType: 'image/jpeg',
    }),
  },
  /**
   * Enable chunked upload
   */
  chunkedUpload: {
    type: Boolean,
    default: false,
  },
  /**
   * Chunk size (bytes)
   */
  chunkSize: {
    type: Number,
    default: 2 * 1024 * 1024, // Default 2MB
  },
  /**
   * Preview image before upload
   */
  previewBeforeUpload: {
    type: Boolean,
    default: false,
  },
  /**
   * File value (for v-model)
   */
  modelValue: {
    type: Array as unknown as PropType<FileUpload.UploadFileItem[]>,
    default: () => [],
  },
  /**
   * Check if upload URL is required (if false, will use uploadFunction or internal API)
   */
  checkUploadUrl: {
    type: Boolean,
    default: true,
  },
  /**
   * Form data name
   */
  formDataName: {
    type: String,
    default: 'upload_files[]',
  },
  /**
   * Fetch remote file info
   */
  fetchRemoteFileInfo: {
    type: Function as PropType<(url: string) => Promise<{ size?: number, type?: string, name?: string }>>,
    default: null,
  },
})

// Define Emits
const emit = defineEmits([
  'update:modelValue',
  'select',
  'beforeUpload',
  'progress',
  'success',
  'error',
  'remove',
  'exceed',
  'exceedSize',
])

// Store upload history to track previously failed uploads across component instances
// Using a module-level variable to persist across component instances
const uploadAttemptCache = new Map<string, { timestamp: number, attempts: number }>()

// Clear old entries from the cache (older than 1 hour)
const cleanupUploadCache = () => {
  const now = Date.now()
  const oneHour = 60 * 60 * 1000 // 1 hour in milliseconds

  for (const [key, value] of uploadAttemptCache.entries()) {
    if (now - value.timestamp > oneHour) {
      uploadAttemptCache.delete(key)
    }
  }
}

// Run cleanup on module load
cleanupUploadCache()

// Type definitions for upload function
interface UploadOptions {
  headers?: Record<string, string>
  onProgress?: (progress: number) => void
  signal?: AbortSignal
  chunked?: boolean
  chunkSize?: number
}

// Function to adapt the API upload function to our UploadFunction interface
const adaptApiUpload = (file: File, options?: UploadOptions): Promise<any> => {
  // Create FormData object for the API
  const formData = new FormData()

  // Append the file to fileList field as required by the API
  formData.append(props.formDataName, file)

  // Use the progress callback if provided
  if (options?.onProgress) {
    // Mock progress since the API doesn't support progress updates
    setTimeout(() => options.onProgress!(50), 500)
    setTimeout(() => options.onProgress!(100), 1000)
  }

  // Support cancellation if signal is provided
  const controller = options?.signal ? new AbortController() : undefined
  if (options?.signal && controller) {
    options.signal.addEventListener('abort', () => {
      controller.abort()
    })
  }

  // Call the API with FormData
  return uploadApi(formData).then(response => response)
}

// Add supportsChunks property to the adapted function
const adaptedUploadApi = adaptApiUpload as FileUpload.UploadFunction
adaptedUploadApi.supportsChunks = false

// Status
const fileInput = ref<HTMLInputElement | null>(null)
const dragOver = ref(false)
const files = ref<File[]>([])
const fileList = ref<FileUpload.FileListItem[]>([])
const isUploading = ref(false)
const activeChunks = ref<Record<string, { xhr: XMLHttpRequest, abort: () => void }>>({})
const totalProgress = ref(0)
const errors = ref<string[]>([])

// Computed properties
const isAvatar = computed(() => props.mode === 'avatar')
const isGallery = computed(() => props.mode === 'gallery')
const isLogo = computed(() => props.mode === 'logo')
const acceptedFileTypes = computed(() => props.accept.split(',').map(type => type.trim()))
const hasFiles = computed(() => fileList.value.length > 0)
const canUpload = computed(() => hasFiles.value && !isUploading.value && !props.disabled)
const fileListStyles = computed(() => ({
  maxHeight: props.fileListMaxHeight,
  overflowY: 'auto' as const,
}))

// Supported image types
const supportedImageTypes = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml',
  'image/bmp',
]

// File type grouping
const fileGroups = computed(() => {
  const groups = {
    images: fileList.value.filter((item: FileUpload.FileListItem) => supportedImageTypes.includes(item.file.type)),
    documents: fileList.value.filter((item: FileUpload.FileListItem) => !supportedImageTypes.includes(item.file.type)),
  }
  return groups
})

// Methods
// Generate unique ID
const generateId = () => {
  return Math.random().toString(36).substring(2, 15)
    + Math.random().toString(36).substring(2, 15)
}

// Open file selector
const selectFiles = () => {
  if (props.disabled) { return }

  if (fileInput.value) {
    fileInput.value.click()
  }
}

// Handle file selection
const handleFileSelect = (event: Event | FileList) => {
  if (props.disabled) { return }

  let selectedFiles: FileList | null = null

  if (event instanceof Event && event.target) {
    const target = event.target as HTMLInputElement
    selectedFiles = target.files
  }
  else if (event instanceof FileList) {
    selectedFiles = event
  }

  if (!selectedFiles || selectedFiles.length === 0) { return }

  processFiles(selectedFiles)
}

// Handle file drop
const handleDrop = (event: DragEvent) => {
  if (props.disabled) { return }

  event.preventDefault()
  dragOver.value = false

  if (!event.dataTransfer) { return }

  processFiles(event.dataTransfer.files)
}

// Handle dragover
const handleDragOver = (event: DragEvent) => {
  if (props.disabled) { return }

  event.preventDefault()
  dragOver.value = true
}

// Handle dragleave
const handleDragLeave = (event: DragEvent) => {
  if (props.disabled) { return }

  event.preventDefault()
  dragOver.value = false
}

// Process files
const processFiles = async (selectedFileList: FileList) => {
  const newFiles: File[] = []

  // Clean up old cache entries
  cleanupUploadCache()

  // Check file count limit - Bug fix: Count existing files in fileList.value (including those from modelValue)
  if (props.maxFiles > 1 && selectedFileList.length + fileList.value.length > props.maxFiles) {
    emit('exceed', {
      files: selectedFileList,
      maxFiles: props.maxFiles,
      currentCount: fileList.value.length,
    })
    window.$toast.add({
      severity: 'error',
      summary: 'Error',
      detail: `You have exceeded the maximum number of files (${props.maxFiles})`,
      life: 3000,
    })
    return
  }

  // 如果maxFiles为1，直接清空现有文件进行覆盖
  if (props.maxFiles === 1) {
    clearFiles()
  }
  // 如果是头像模式，先清空现有文件
  else if (isAvatar.value) {
    clearFiles()
  }

  console.log('fileList.value', fileList.value)

  // Process each file
  for (let i = 0; i < selectedFileList.length; i++) {
    const file = selectedFileList[i]

    // Check file size
    if (props.maxSize > 0 && file.size > props.maxSize) {
      emit('exceedSize', {
        file,
        maxSize: props.maxSize,
      })
      window.$toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'You have exceeded the maximum file size',
        life: 3000,
      })
      continue
    }

    // Multiple files mode or no files selected yet
    if (props.multiple || files.value.length === 0) {
      newFiles.push(file)
    }
    else {
      // Single file mode, replace existing file
      newFiles.push(file)
      clearFiles()
    }
  }

  if (newFiles.length === 0) { return }

  // Add files to list
  await addFiles(newFiles)

  // Auto upload - support all upload methods
  if (props.autoUpload && (props.uploadUrl || props.uploadFunction || (typeof adaptedUploadApi === 'function'))) {
    upload()
  }
}

// Validate file type
const isValidFileType = (file: File) => {
  if (!props.accept) { return true }

  const acceptedTypes = acceptedFileTypes.value
  const fileType = file.type
  const fileExtension = file.name.split('.').pop()?.toLowerCase()

  // Check MIME type
  if (acceptedTypes.some((type) => {
    // Exact match MIME type
    if (type === fileType) { return true }
    // Match type group, like image/*
    if (type.endsWith('/*') && fileType.startsWith(type.replace('/*', '/'))) { return true }
    // Match extension
    if (type.startsWith('.') && `.${fileExtension}` === type) { return true }
    return false
  })) {
    return true
  }

  // Removed error notification since we now handle errors in the UI
  return false
}

// Add files to list
const addFiles = async (newFiles: File[]) => {
  const addedFiles = []

  for (const file of newFiles) {
    const fileId = generateId()
    let preview = ''
    let fileStatus: FileUpload.UploadStatus = 'pending'
    let fileError: string | undefined

    // Check file type validation
    const isValidType = props.accept ? isValidFileType(file) : true

    if (!isValidType) {
      fileStatus = 'error'
      fileError = `Unsupported file format: must be a file of type: ${props.accept}`
    }

    // Create preview for image files
    if (supportedImageTypes.includes(file.type)) {
      preview = URL.createObjectURL(file)
    }

    // Check if this file had previously failed uploads
    const fileFingerprint = generateFileFingerprint(file)
    const previousAttempt = uploadAttemptCache.get(fileFingerprint)

    // Create file item with potential warning
    const fileItem: FileUpload.FileListItem = {
      file,
      id: fileId,
      progress: 0,
      status: fileStatus,
      preview,
      error: fileError,
      // Add warning if previously failed to upload
      warning: previousAttempt ? `Note: This file has failed ${previousAttempt.attempts} upload attempt(s)` : undefined,
    }

    fileList.value.push(fileItem)
    // Only add valid files to the addedFiles array for emitting
    if (fileStatus === 'pending') {
      addedFiles.push(file)
    }

    // If file previously failed and has multiple attempts, show a notification
    if (previousAttempt && previousAttempt.attempts > 1 && fileStatus === 'pending') {
      window.$toast.add({
        severity: 'warn',
        summary: 'Previous Upload Failed',
        detail: `File "${file.name}" failed to upload previously, retrying`,
        life: 3000,
      })
    }
  }

  // Only add valid files to files.value (which will be uploaded)
  const validFiles = newFiles.filter(file => props.accept ? isValidFileType(file) : true)
  files.value = [...files.value, ...validFiles]

  if (addedFiles.length > 0) {
    emit('select', addedFiles)
  }
  updateModelValue()
}

// Update model value
const updateModelValue = () => {
  if (props.multiple) {
    // 获取成功上传的文件和错误文件
    const resultFiles = fileList.value
      .map((item: FileUpload.FileListItem) => {
        if (item.status === 'success') {
          // 标准化输出格式 - 成功文件
          let fileData: FileUpload.UploadFileItem = {
            url: '',
            name: item.file.name,
            size: item.file.size,
            type: item.file.type,
            status: 'success',
          }

          // 处理不同格式的response
          if (item.response) {
            if (typeof item.response === 'string') {
              // 直接是URL字符串
              fileData.url = item.response
            }
            else if (typeof item.response === 'object') {
              if ('url' in item.response) {
                // 标准格式
                fileData.url = item.response.url
              }
              else if (Array.isArray(item.response.file_path) && item.response.file_path.length > 0) {
                // 特定API返回的格式
                fileData.url = item.response.file_path[0]
              }
              else {
                // 其他格式，尝试获取可能的URL字段
                const possibleUrlFields = ['url', 'path', 'filePath', 'file_url', 'fileUrl', 'src', 'source']
                for (const field of possibleUrlFields) {
                  if (field in item.response && typeof item.response[field] === 'string') {
                    fileData.url = item.response[field]
                    break
                  }
                }

                // 如果没有找到URL，保留整个response对象以备后用
                if (!fileData.url) {
                  fileData = { ...fileData, ...item.response }
                }
              }
            }
          }

          // 如果没有提取到URL，使用文件对象的一些属性
          if (!fileData.url && item.preview) {
            fileData.url = item.preview
          }

          return fileData
        }
        else if (item.status === 'error') {
          // 错误文件
          return {
            name: item.file.name,
            size: item.file.size,
            type: item.file.type,
            status: 'error',
            error: item.error,
            preview: item.preview || null,
            url: '', // 错误时url为空
          }
        }
        else if (item.status === 'pending' || item.status === 'uploading') {
          // 未上传完成文件
          return {
            name: item.file.name,
            size: item.file.size,
            type: item.file.type,
            status: item.status,
            preview: item.preview || null,
            url: '', // 未上传成功时url为空
          }
        }
        return null
      })
      .filter(item => item !== null)

    emit('update:modelValue', resultFiles)
  }
  else if (fileList.value.length > 0) {
    const lastFile = fileList.value[fileList.value.length - 1]

    if (lastFile.status === 'success') {
      // 单文件模式，成功上传
      let fileData: FileUpload.UploadFileItem = {
        url: '',
        name: lastFile.file.name,
        size: lastFile.file.size,
        type: lastFile.file.type,
        status: 'success',
      }

      // 处理不同格式的response
      if (lastFile.response) {
        if (typeof lastFile.response === 'string') {
          fileData.url = lastFile.response
        }
        else if (typeof lastFile.response === 'object') {
          if ('url' in lastFile.response) {
            fileData.url = lastFile.response.url
          }
          else if (Array.isArray(lastFile.response.file_path) && lastFile.response.file_path.length > 0) {
            fileData.url = lastFile.response.file_path[0]
          }
          else {
            // 尝试查找可能的URL字段
            const possibleUrlFields = ['url', 'path', 'filePath', 'file_url', 'fileUrl', 'src', 'source']
            for (const field of possibleUrlFields) {
              if (field in lastFile.response && typeof lastFile.response[field] === 'string') {
                fileData.url = lastFile.response[field]
                break
              }
            }

            // 如果没有找到URL，保留整个response对象
            if (!fileData.url) {
              fileData = { ...fileData, ...lastFile.response }
            }
          }
        }
      }

      // 如果没有提取到URL，使用文件预览
      if (!fileData.url && lastFile.preview) {
        fileData.url = lastFile.preview
      }

      // 返回单项数组，保持一致性
      emit('update:modelValue', [fileData])
    }
    else {
      // 文件还未上传成功或有错误，返回包含状态的对象，但包装为数组
      emit('update:modelValue', [{
        name: lastFile.file.name,
        size: lastFile.file.size,
        type: lastFile.file.type,
        status: lastFile.status,
        error: lastFile.error, // Include error message if present
        preview: lastFile.preview || null,
        url: '', // 未上传成功时url为空
      }])
    }
  }
  else {
    // 无文件时始终返回空数组
    emit('update:modelValue', [])
  }
}

// Clear file list
const clearFiles = () => {
  // Release preview URLs
  fileList.value.forEach((item: FileUpload.FileListItem) => {
    if (item.preview) {
      URL.revokeObjectURL(item.preview)
    }
  })

  files.value = []
  fileList.value = []
  updateModelValue()
}

// Remove file
const removeFile = (fileId: string) => {
  const fileIndex = fileList.value.findIndex((item: FileUpload.FileListItem) => item.id === fileId)

  if (fileIndex === -1) { return }

  const fileItem = fileList.value[fileIndex]

  // If uploading, abort the upload
  if (fileItem.status === 'uploading' && fileId in activeChunks.value) {
    activeChunks.value[fileId].abort()
    delete activeChunks.value[fileId]
  }

  // Release preview URL
  if (fileItem.preview) {
    URL.revokeObjectURL(fileItem.preview)
  }

  // Remove from list
  fileList.value.splice(fileIndex, 1)
  files.value = files.value.filter(file => file !== fileItem.file)

  emit('remove', fileItem.file)
  updateModelValue()
}

// Compress image file
const compressImage = (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    if (!props.imageCompressionOptions.enabled || !file.type.startsWith('image/')) {
      resolve(file)
      return
    }

    const reader = new FileReader()
    reader.readAsDataURL(file)

    reader.onload = (event) => {
      const img = new Image()
      img.src = event.target?.result as string

      img.onload = () => {
        const { maxWidth, maxHeight, quality, mimeType } = props.imageCompressionOptions
        const canvas = document.createElement('canvas')
        let width = img.width
        let height = img.height

        // Scale image
        if (width > maxWidth) {
          height = Math.round((height * maxWidth) / width)
          width = maxWidth
        }

        if (height > maxHeight) {
          width = Math.round((width * maxHeight) / height)
          height = maxHeight
        }

        canvas.width = width
        canvas.height = height

        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('Could not get canvas context'))
          return
        }

        ctx.drawImage(img, 0, 0, width, height)

        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Image compression failed'))
              return
            }

            const compressedFile = new File([blob], file.name, {
              type: mimeType || file.type,
              lastModified: Date.now(),
            })

            resolve(compressedFile)
          },
          mimeType || file.type,
          quality,
        )
      }

      img.onerror = () => {
        reject(new Error('Failed to load image'))
      }
    }

    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }
  })
}

// Get file icon
const getFileIcon = (fileType: string) => {
  if (fileType.startsWith('image/')) { return 'pi pi-image' }
  if (fileType.startsWith('video/')) { return 'pi pi-video' }
  if (fileType.startsWith('audio/')) { return 'pi pi-volume-up' }
  if (fileType.includes('pdf')) { return 'pi pi-file-pdf' }
  if (fileType.includes('word') || fileType.includes('document')) { return 'pi pi-file-word' }
  if (fileType.includes('excel') || fileType.includes('sheet')) { return 'pi pi-file-excel' }
  if (fileType.includes('powerpoint') || fileType.includes('presentation')) { return 'pi pi-file-powerpoint' }
  if (fileType.includes('zip') || fileType.includes('compressed')) { return 'pi pi-folder' }
  return 'pi pi-file'
}

// Get readable file size
const getReadableFileSize = (bytes: number) => {
  if (bytes === 0) { return '0 MB' }

  // 始终使用 MB 作为单位
  return `${(bytes / (1024 * 1024)).toFixed(2)} MB`
}

// Normal file upload
const uploadFile = (fileItem: FileUpload.FileListItem) => {
  // Reset the retry counter when manually uploading
  resetUploadAttempts(fileItem.id)
  return new Promise<void>((resolve, reject) => {
    const { file, id } = fileItem

    // Generate a file fingerprint for cache lookup
    const fileFingerprint = generateFileFingerprint(file)

    // Use function-based upload approach if available
    if (props.uploadFunction || adaptedUploadApi) {
      // Use custom function or adapt the API function
      const uploadFn = props.uploadFunction || adaptedUploadApi

      // Set status to uploading
      fileItem.status = 'uploading'
      fileItem.progress = 0

      // Create an abort controller to allow cancellation
      const abortController = new AbortController()
      activeChunks.value[id] = {
        xhr: null as any,
        abort: () => abortController.abort(),
      }

      // Setup a progress tracker if the upload function supports it
      const onProgress = (progress: number) => {
        fileItem.progress = progress
        updateTotalProgress()
        emit('progress', { file, progress })
      }

      // Call the upload function with the file and optional progress callback
      uploadFn(file, {
        headers: props.headers,
        onProgress,
        signal: abortController.signal,
      })
        .then((response: any) => {
          const { code, data, message } = response
          fileItem.status = 'success'
          if (code === 0) {
            if (Array.isArray(data?.file_path) && data?.file_path.length > 0) {
              fileItem.response = data.file_path[0]
            }
            else {
              fileItem.response = response
            }
            // Clear the file from the cache on successful upload
            uploadAttemptCache.delete(fileFingerprint)
            updateModelValue()
            emit('success', { file, response })
            resolve()
          }
          else {
            fileItem.status = 'error'
            fileItem.error = message || 'Invalid server response format'
            emit('error', { file, error: fileItem.error })

            // Record the failed attempt in the cache
            const existingAttempt = uploadAttemptCache.get(fileFingerprint) || { timestamp: Date.now(), attempts: 0 }
            uploadAttemptCache.set(fileFingerprint, {
              timestamp: Date.now(),
              attempts: existingAttempt.attempts + 1,
            })

            // 添加错误提示
            if (window.$toast) {
              window.$toast.add({
                severity: 'error',
                summary: 'Error',
                detail: message || 'Invalid server response format',
                life: 3000,
              })
            }
            fileItem.status = 'error'
            fileItem.error = message || 'Invalid server response format'
            emit('error', { file, error: fileItem.error })
            updateModelValue()
            reject(new Error(message || 'Invalid server response format'))
          }
        })
        .catch((error: Error) => {
          // Don't update status if it was aborted (will be handled elsewhere)
          if (error.name === 'AbortError') { return }
          fileItem.status = 'error'
          fileItem.error = error.message || 'Upload failed'
          emit('error', { file, error: fileItem.error })

          // Record the failed attempt in the cache
          const existingAttempt = uploadAttemptCache.get(fileFingerprint) || { timestamp: Date.now(), attempts: 0 }
          uploadAttemptCache.set(fileFingerprint, {
            timestamp: Date.now(),
            attempts: existingAttempt.attempts + 1,
          })

          reject(error)
        })

      return
    }

    // Fall back to traditional XHR approach if no function is provided
    const xhr = new XMLHttpRequest()
    const formData = new FormData()

    // Set file field name to 'file'
    formData.append('file', file)

    // Add custom headers before upload
    xhr.open('POST', props.uploadUrl)

    // Add custom headers (must be after open)
    Object.entries(props.headers).forEach(([key, value]) => {
      xhr.setRequestHeader(key, value.toString())
    })

    // Set upload progress listener
    xhr.upload.addEventListener('progress', (e) => {
      if (e.lengthComputable) {
        const percent = Math.round((e.loaded * 100) / e.total)
        fileItem.progress = percent
        updateTotalProgress()
        emit('progress', { file, progress: percent })
      }
    })

    // Modified completion callback to handle cache
    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          fileItem.status = 'success'
          fileItem.response = response
          // Clear the file from the cache on successful upload
          uploadAttemptCache.delete(fileFingerprint)
          updateModelValue()
          emit('success', { file, response })
          resolve()
        }
        catch {
          fileItem.status = 'error'
          fileItem.error = 'Invalid server response format'
          emit('error', { file, error: fileItem.error })

          // Record the failed attempt in the cache
          const existingAttempt = uploadAttemptCache.get(fileFingerprint) || { timestamp: Date.now(), attempts: 0 }
          uploadAttemptCache.set(fileFingerprint, {
            timestamp: Date.now(),
            attempts: existingAttempt.attempts + 1,
          })

          // 添加错误提示
          if (window.$toast) {
            window.$toast.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Invalid server response format',
              life: 3000,
            })
          }
          reject(new Error('Invalid server response format'))
        }
      }
      else {
        fileItem.status = 'error'
        fileItem.error = `${xhr.status}: ${xhr.statusText}`
        emit('error', { file, error: fileItem.error })

        // Record the failed attempt in the cache
        const existingAttempt = uploadAttemptCache.get(fileFingerprint) || { timestamp: Date.now(), attempts: 0 }
        uploadAttemptCache.set(fileFingerprint, {
          timestamp: Date.now(),
          attempts: existingAttempt.attempts + 1,
        })

        // 添加错误提示
        if (window.$toast) {
          window.$toast.add({
            severity: 'error',
            summary: 'Error',
            detail: `${xhr.status}: ${xhr.statusText}`,
            life: 3000,
          })
        }

        reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`))
      }
    }

    // Set error callback
    xhr.onerror = () => {
      fileItem.status = 'error'
      fileItem.error = 'Network error'
      console.log('Network error', fileItem)
      emit('error', { file, error: 'Network error' })

      // Record the failed attempt in the cache
      const existingAttempt = uploadAttemptCache.get(fileFingerprint) || { timestamp: Date.now(), attempts: 0 }
      uploadAttemptCache.set(fileFingerprint, {
        timestamp: Date.now(),
        attempts: existingAttempt.attempts + 1,
      })

      // 添加错误提示
      if (window.$toast) {
        window.$toast.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Network error',
          life: 3000,
        })
      }

      reject(new Error('Network error'))
    }

    // Set abort callback
    xhr.onabort = () => {
      fileItem.status = 'error'
      fileItem.error = 'Upload cancelled'
      fileItem.progress = 0
      emit('error', { file, error: 'Upload cancelled' })

      // 添加错误提示 - 取消可能是用户主动操作，所以使用 info 级别
      if (window.$toast) {
        window.$toast.add({
          severity: 'info',
          summary: 'Info',
          detail: 'Upload cancelled',
          life: 3000,
        })
      }

      reject(new Error('Upload cancelled'))
    }

    // Save xhr object to be able to abort upload
    activeChunks.value[id] = {
      xhr,
      abort: () => xhr.abort(),
    }

    // Send request
    xhr.send(formData)
  })
}

// Generate a fingerprint for a file to use as a cache key
const generateFileFingerprint = (file: File): string => {
  // Combine file properties to create a unique identifier
  return `${file.name}_${file.size}_${file.lastModified}`
}

// Chunked file upload - Fix async Promise executor
const uploadFileInChunks = (fileItem: FileUpload.FileListItem) => {
  return new Promise<void>((resolve, reject) => {
    const { file, id } = fileItem
    const chunkSize = props.chunkSize
    const totalChunks = Math.ceil(file.size / chunkSize)
    let currentChunk = 0
    let overallProgress = 0

    // Use function-based upload approach if available and supports chunked uploads
    if ((props.uploadFunction || adaptedUploadApi) && file.size > chunkSize) {
      // Use custom function or adapt the API function
      const uploadFn = props.uploadFunction || adaptedUploadApi

      // Skip chunked upload if the function doesn't support it directly
      if (!uploadFn.supportsChunks) {
        // If the API doesn't support chunks, fall back to single upload
        uploadFile(fileItem).then(resolve).catch(reject)
        return
      }

      // Set status to uploading
      fileItem.status = 'uploading'
      fileItem.progress = 0

      // Create an abort controller to allow cancellation
      const abortController = new AbortController()
      activeChunks.value[id] = {
        xhr: null as any,
        abort: () => abortController.abort(),
      }

      // Setup a progress tracker
      const onProgress = (progress: number) => {
        fileItem.progress = progress
        updateTotalProgress()
        emit('progress', { file, progress })
      }

      // Call the upload function with chunk information
      uploadFn(file, {
        headers: props.headers,
        onProgress,
        signal: abortController.signal,
        chunked: true,
        chunkSize,
      })
        .then((response: any) => {
          fileItem.status = 'success'
          fileItem.response = response
          updateModelValue()
          emit('success', { file, response })
          resolve()
        })
        .catch((error: Error) => {
          // Don't update status if it was aborted (will be handled elsewhere)
          if (error.name === 'AbortError') { return }

          fileItem.status = 'error'
          fileItem.error = error.message || 'Upload failed'
          emit('error', { file, error: fileItem.error })

          // 添加错误提示
          if (window.$toast) {
            window.$toast.add({
              severity: 'error',
              summary: 'Upload Error',
              detail: error.message || 'Chunked upload failed',
              life: 3000,
            })
          }

          reject(error)
        })

      return
    }

    // Fall back to traditional XHR chunked approach
    const uploadNextChunk = async () => {
      if (currentChunk >= totalChunks) {
        fileItem.status = 'success'
        updateModelValue()
        resolve()
        return
      }

      const start = currentChunk * chunkSize
      const end = Math.min(file.size, start + chunkSize)
      const chunk = file.slice(start, end)

      const xhr = new XMLHttpRequest()
      const formData = new FormData()

      // Add chunk information
      formData.append('file', chunk, file.name)
      formData.append('chunk', currentChunk.toString())
      formData.append('chunks', totalChunks.toString())
      formData.append('fileName', file.name)

      // Add custom headers before upload
      xhr.open('POST', props.uploadUrl)

      // Add custom headers (must be after open)
      Object.entries(props.headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value.toString())
      })

      // Set upload progress listener
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          // Calculate current chunk progress
          const chunkProgress = Math.round((e.loaded * 100) / e.total)

          // Calculate overall progress (each chunk's proportion of the total)
          overallProgress = Math.round(
            ((currentChunk + chunkProgress / 100) / totalChunks) * 100,
          )

          fileItem.progress = overallProgress
          updateTotalProgress()
          emit('progress', { file, progress: overallProgress })
        }
      })

      // Set completion callback
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            if (currentChunk === totalChunks - 1) {
              // Last chunk, parse response
              const response = JSON.parse(xhr.responseText)
              fileItem.response = response
              emit('success', { file, response })
            }

            currentChunk++
            uploadNextChunk()
          }
          catch {
            fileItem.status = 'error'
            fileItem.error = 'Invalid server response format'
            emit('error', { file, error: fileItem.error })

            // 添加错误提示
            if (window.$toast) {
              window.$toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Invalid server response format',
                life: 3000,
              })
            }

            reject(new Error('Invalid server response format'))
          }
        }
        else {
          fileItem.status = 'error'
          fileItem.error = `${xhr.status}: ${xhr.statusText}`
          emit('error', { file, error: fileItem.error })

          // 添加错误提示
          if (window.$toast) {
            window.$toast.add({
              severity: 'error',
              summary: 'Error',
              detail: `Chunk upload failed: ${xhr.status} ${xhr.statusText}`,
              life: 3000,
            })
          }

          reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`))
        }
      }

      // Set error callback
      xhr.onerror = () => {
        fileItem.status = 'error'
        fileItem.error = 'Network error'
        emit('error', { file, error: 'Network error' })

        // 添加错误提示
        if (window.$toast) {
          window.$toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Network error during chunk upload',
            life: 3000,
          })
        }

        reject(new Error('Network error'))
      }

      // Set abort callback
      xhr.onabort = () => {
        fileItem.status = 'error'
        fileItem.error = 'Upload cancelled'
        fileItem.progress = 0
        emit('error', { file, error: 'Upload cancelled' })

        // 添加错误提示 - 取消可能是用户主动操作，所以使用 info 级别
        if (window.$toast) {
          window.$toast.add({
            severity: 'info',
            summary: 'Info',
            detail: 'Chunked upload cancelled',
            life: 3000,
          })
        }

        reject(new Error('Upload cancelled'))
      }

      // Save xhr object to be able to abort upload
      activeChunks.value[id] = {
        xhr,
        abort: () => xhr.abort(),
      }

      // Send request
      xhr.send(formData)
    }

    // Start uploading the first chunk
    uploadNextChunk().catch(reject)
  })
}

// Update total progress
const updateTotalProgress = () => {
  if (fileList.value.length === 0) {
    totalProgress.value = 0
    return
  }

  const total = fileList.value.reduce((sum: number, item: FileUpload.FileListItem) => sum + item.progress, 0)
  totalProgress.value = Math.round(total / fileList.value.length)
}

// Validate before upload
const validateBeforeUpload = () => {
  // We have adaptedUploadApi as fallback, so we only show error if explicitly checking for URL
  if (props.checkUploadUrl && !props.uploadFunction && !props.uploadUrl && !adaptedUploadApi) {
    errors.value.push('No upload function or URL provided')
    return false
  }

  if (fileList.value.length === 0) {
    errors.value.push('No files selected')
    return false
  }

  // Trigger beforeUpload event, allow external validation
  const beforeUploadEvent = {
    files: files.value,
    cancel: false,
  }

  emit('beforeUpload', beforeUploadEvent)

  if (beforeUploadEvent.cancel) {
    return false
  }

  return true
}

// Upload all files
const upload = async () => {
  errors.value = []

  if (!validateBeforeUpload()) {
    // 在验证失败时添加 toast 提示
    if (errors.value.length > 0 && window.$toast) {
      window.$toast.add({
        severity: 'error',
        summary: 'Validation Error',
        detail: errors.value[0], // 显示第一个错误消息
        life: 3000,
      })
    }
    return
  }

  isUploading.value = true

  try {
    // Filter pending files - explicitly exclude error files
    const pendingFiles = fileList.value.filter((item: FileUpload.FileListItem) =>
      item.status === 'pending' && item.error === undefined,
    )

    for (const fileItem of pendingFiles) {
      fileItem.status = 'uploading'
      fileItem.progress = 0

      try {
        let fileToUpload = fileItem.file

        // If image compression is enabled, try to compress
        if (props.imageCompressionOptions.enabled && fileItem.file.type.startsWith('image/')) {
          fileToUpload = await compressImage(fileItem.file)
        }

        // Choose upload method based on whether chunked upload is enabled
        if (props.chunkedUpload && fileToUpload.size > props.chunkSize) {
          await uploadFileInChunks(fileItem)
        }
        else {
          await uploadFile(fileItem)
        }
      }
      catch (error) {
        // Single file upload failure doesn't affect other files
        console.error('File upload failed:', fileItem.file.name, error)
      }
    }
  }
  catch (error) {
    // 处理整体上传过程中的错误
    if (window.$toast) {
      window.$toast.add({
        severity: 'error',
        summary: 'Upload Error',
        detail: error instanceof Error ? error.message : 'An error occurred during upload',
        life: 3000,
      })
    }
  }
  finally {
    isUploading.value = false
  }
}

// Abort all uploads
const abort = () => {
  Object.values(activeChunks.value).forEach(chunk => chunk.abort())
  activeChunks.value = {}

  fileList.value.forEach((item: FileUpload.FileListItem) => {
    if (item.status === 'uploading') {
      item.status = 'error'
      item.error = 'Upload cancelled'
      item.progress = 0
    }
  })

  isUploading.value = false
  totalProgress.value = 0
}

// Submit all files
const submit = () => {
  if (canUpload.value) {
    upload()
  }
}

// Export methods
defineExpose({
  upload,
  abort,
  clearFiles,
  submit,
})

// Component cleanup before unmount
onBeforeUnmount(() => {
  // Abort all uploads
  Object.values(activeChunks.value).forEach(chunk => chunk.abort())

  // Release all preview URLs
  fileList.value.forEach((item: FileUpload.FileListItem) => {
    if (item.preview) {
      URL.revokeObjectURL(item.preview)
    }
  })
})

// 处理modelValue初始值以实现回显
const initializeFromModelValue = () => {
  if (!props.modelValue) { return }

  // 处理数组类型的modelValue
  if (Array.isArray(props.modelValue) && props.modelValue.length > 0) {
    props.modelValue.forEach((item) => {
      // 如果item是字符串，当作URL处理
      if (typeof item === 'string') {
        addExistingFile(item)
      }
      // 如果item是对象且有url属性或status属性
      else if (typeof item === 'object' && item !== null) {
        const fileItem = item as FileUpload.UploadFileItem
        // 如果文件有状态信息，传递给addExistingFile进行处理
        if ('status' in fileItem && fileItem.status === 'error') {
          // 处理错误状态的文件
          addExistingFile(
            fileItem.url || '',
            fileItem.name,
            fileItem.size,
            'error',
            fileItem.error,
          )
        }
        else if ('url' in fileItem) {
          // 处理正常的文件（有URL属性）
          addExistingFile(fileItem.url, fileItem.name, fileItem.size)
        }
      }
    })
  }
  // 处理字符串类型的modelValue (URL) - 为了向后兼容
  else if (typeof props.modelValue === 'string' && props.modelValue) {
    addExistingFile(props.modelValue)
  }
  // 处理单个对象的modelValue - 为了向后兼容
  else if (typeof props.modelValue === 'object' && props.modelValue !== null && 'url' in props.modelValue) {
    const fileItem = props.modelValue as FileUpload.UploadFileItem
    addExistingFile(fileItem.url, fileItem.name, fileItem.size)
  }
}

// 添加已存在的文件到文件列表中
const addExistingFile = async (
  url: string,
  name?: string,
  _size?: number,
  fileStatus?: FileUpload.UploadStatus,
  fileError?: string,
) => {
  let fileName = name || getFileNameFromUrl(url)
  let fileType = getFileTypeFromUrl(url)
  const fileId = generateId()

  // 如果提供了获取远程文件信息的函数，尝试获取真实信息
  if (props.fetchRemoteFileInfo) {
    try {
      const fileInfo = await props.fetchRemoteFileInfo(url)
      if (fileInfo.type) { fileType = fileInfo.type }
      if (fileInfo.name) { fileName = fileInfo.name }
    }
    catch (error) {
      console.warn('Failed to fetch remote file info:', error)
    }
  }

  let preview = ''
  if (supportedImageTypes.includes(fileType)) {
    preview = url
  }

  // 对于网络图片，添加获取图片信息的逻辑
  if (fileType.startsWith('image/') && !_size) {
    // 创建一个临时的Image对象来获取图片尺寸
    const img = new Image()
    img.onload = () => {
      // 估算图片大小 (像素数 * 每像素平均字节数)
      // 这是一个粗略估计，真实值需要通过网络请求的Content-Length获取
      const estimatedSize = Math.round(img.width * img.height * 0.25) // 假设平均每像素0.25字节

      // 更新文件项的大小信息
      const fileIndex = fileList.value.findIndex((item: FileUpload.FileListItem) => item.id === fileId)
      if (fileIndex !== -1) {
        // 更新虚拟文件对象的大小
        const updatedFile = new File(
          [new ArrayBuffer(estimatedSize)],
          fileName,
          { type: fileType },
        )
        fileList.value[fileIndex].file = updatedFile

        // 触发UI更新
        fileList.value = [...fileList.value]
      }
    }

    // 设置src以触发加载事件
    img.src = url
  }

  // 创建初始文件项，大小可能稍后更新
  const fileItem: FileUpload.FileListItem = {
    // 创建一个虚拟文件对象
    file: new File([], fileName, { type: fileType }),
    id: fileId,
    progress: 100,
    status: fileStatus || 'success', // 使用传入的状态或默认为success
    response: { url },
    preview: preview || '',
  }

  // 如果有错误信息，添加到文件项中
  if (fileStatus === 'error' && fileError) {
    fileItem.error = fileError
  }

  fileList.value.push(fileItem)
}

// 从URL中提取文件名
const getFileNameFromUrl = (url: string): string => {
  const parts = url.split('/')
  const fileName = parts[parts.length - 1]

  // 移除查询参数
  return fileName.split('?')[0]
}

// 从URL或文件名中猜测文件类型
const getFileTypeFromUrl = (url: string): string => {
  const fileName = getFileNameFromUrl(url)
  const extension = fileName.split('.').pop()?.toLowerCase() || ''

  // 简单的MIME类型映射
  const mimeMap: Record<string, string> = {
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    svg: 'image/svg+xml',
    webp: 'image/webp',
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    zip: 'application/zip',
    mp3: 'audio/mpeg',
    mp4: 'video/mp4',
  }

  return mimeMap[extension] || 'application/octet-stream'
}

// 监听modelValue变化
watch(() => props.modelValue, () => {
  // 如果fileList为空，说明可能是初始赋值或者被清空后重新赋值
  if (fileList.value.length === 0) {
    initializeFromModelValue()
  }
}, { deep: true, immediate: true })

// Reset upload attempts for a specific file
const resetUploadAttempts = (fileId: string) => {
  const fileItem = fileList.value.find(item => item.id === fileId)
  if (fileItem) {
    const fingerprint = generateFileFingerprint(fileItem.file)
    uploadAttemptCache.delete(fingerprint)

    // Remove warning
    if (fileItem.warning) {
      delete fileItem.warning
      // Force reactivity update
      fileList.value = [...fileList.value]
    }
  }
}
</script>

<template>
  <div
    class="p-fileupload p-component" :class="{
      'p-fileupload-advanced': !isAvatar,
      'p-fileupload-avatar': isAvatar,
      'p-fileupload-gallery': isGallery,
      'p-fileupload-logo': isLogo,
      'p-disabled': disabled,
    }"
  >
    <!-- Upload Error Messages -->
    <div v-if="errors.length > 0" class="p-fileupload-errors">
      <Message v-for="(error, index) in errors" :key="index" severity="error" :closable="true">
        {{ error }}
      </Message>
    </div>

    <!-- Avatar Mode -->
    <div v-if="isAvatar" class="p-fileupload-avatar-content">
      <div
        class="p-fileupload-avatar-dropzone" :class="{ 'p-fileupload-dragover': dragOver }" @click="selectFiles"
        @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop"
      >
        <div v-if="fileList.length === 0 || !fileList[0].preview" class="p-fileupload-avatar-placeholder">
          <i class="pi pi-user p-fileupload-avatar-icon" />
          <slot name="upload-text">
            <div class="p-fileupload-text">
              Drop or click to upload avatar
            </div>
          </slot>
        </div>
        <div v-else class="p-fileupload-avatar-preview">
          <img :src="fileList[0].preview" alt="Avatar Preview">
          <div
            v-if="fileList[0]?.progress !== undefined && fileList[0].status === 'uploading'"
            class="p-fileupload-avatar-progress"
          >
            <div class="p-fileupload-avatar-progress-text">
              {{ fileList[0].progress }}%
            </div>
            <ProgressBar :value="fileList[0].progress" />
          </div>
          <div class="p-fileupload-avatar-overlay">
            <div class="p-fileupload-avatar-overlay-content">
              <i class="pi pi-camera" />
              <span>Change avatar</span>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="fileList.length > 0 && !autoUpload && !isUploading && fileList[0].status === 'pending'"
        class="p-fileupload-avatar-actions"
      >
        <Button icon="pi pi-upload" class="p-button-rounded p-button-success" :disabled="!canUpload" @click="upload" />
        <Button icon="pi pi-times" class="p-button-rounded p-button-danger" @click="clearFiles" />
      </div>
      <ProgressBar
        v-if="isUploading && fileList.filter((item: FileUpload.FileListItem) => item.status === 'uploading').length > 1"
        :value="totalProgress"
      />
    </div>

    <!-- Gallery Mode -->
    <div v-else-if="isGallery" class="p-fileupload-gallery-content">
      <div
        class="p-fileupload-gallery-dropzone" :class="{ 'p-fileupload-dragover': dragOver }" @click="selectFiles"
        @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop"
      >
        <div v-if="fileList.length === 0" class="p-fileupload-gallery-placeholder">
          <slot name="upload-icon">
            <i class="pi pi-images p-fileupload-gallery-icon" />
          </slot>
          <slot name="upload-text">
            <span>Drop or click to upload images</span>
          </slot>
        </div>
      </div>

      <div v-if="showFileList && fileList.length > 0" class="p-fileupload-gallery-files" :style="fileListStyles">
        <div class="p-fileupload-gallery-grid">
          <div
            v-for="item in fileGroups.images" :key="item.id" class="p-fileupload-gallery-item" :class="{
              'p-fileupload-gallery-item-uploading': item.status === 'uploading',
              'p-fileupload-gallery-item-success': item.status === 'success',
              'p-fileupload-gallery-item-error': item.status === 'error',
            }"
          >
            <div class="p-fileupload-gallery-item-preview">
              <img v-if="item.preview" :src="item.preview" :alt="item.file.name">
              <i v-else :class="getFileIcon(item.file.type)" />
            </div>
            <div class="p-fileupload-gallery-item-overlay">
              <div class="p-fileupload-gallery-item-info">
                <span class="p-fileupload-gallery-item-name">{{ item.file.name }}</span>
                <span class="p-fileupload-gallery-item-size">{{ getReadableFileSize(item.file.size)
                }}</span>
              </div>
              <div class="p-fileupload-gallery-item-actions">
                <Button
                  v-if="item.status === 'pending'" icon="pi pi-upload"
                  class="p-button-rounded p-button-outlined p-button-sm p-button-success"
                  @click="() => { if (canUpload) uploadFile(item) }"
                />
                <Button
                  icon="pi pi-times" class="p-button-rounded p-button-outlined p-button-sm p-button-danger"
                  @click="() => removeFile(item.id)"
                />
              </div>
              <ProgressBar
                v-if="item.status === 'uploading'" :value="item.progress"
                class="p-fileupload-gallery-item-progress"
              />
              <Badge v-if="item.status === 'success'" value="Success" severity="success" />
              <Badge v-if="item.status === 'error'" value="Error" severity="danger" />
            </div>
          </div>
        </div>
      </div>

      <div v-if="!autoUpload && !isUploading && hasFiles" class="p-fileupload-gallery-actions">
        <Button icon="pi pi-upload" label="Upload All" :disabled="!canUpload" @click="upload" />
        <Button icon="pi pi-times" label="Clear" class="p-button-outlined p-button-danger" @click="clearFiles" />
      </div>

      <ProgressBar
        v-if="isUploading && fileList.filter((item: FileUpload.FileListItem) => item.status === 'uploading').length > 1"
        :value="totalProgress"
      />
    </div>
    <!-- Logo Mode -->
    <div v-else-if="isLogo" class="p-fileupload-logo-content">
      <div
        class="p-fileupload-logo-dropzone" :class="{ 'p-fileupload-dragover': dragOver }" @click="selectFiles"
        @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop"
      >
        <div v-if="fileList.length === 0 || !fileList[0]?.response" class="p-fileupload-logo-placeholder">
          <i class="pi pi-plus p-fileupload-logo-icon" />
          <span>Drop or click to upload logo</span>
        </div>
        <div v-else class="p-fileupload-logo-preview">
          <img :src="fileList[0].preview || fileList[0]?.response?.url" alt="Logo Preview">
          <div v-if="fileList[0]?.progress !== undefined && fileList[0].status === 'uploading'" class="p-fileupload-logo-progress">
            <div class="p-fileupload-logo-progress-text">
              {{ fileList[0].progress }}%
            </div>
            <ProgressBar :value="fileList[0].progress" />
          </div>
        </div>
        <div v-if="fileList.length !== 0" class="absolute top-2 right-2">
          <i class="pi pi-times-circle" style="font-size: 18px;" @click.stop="clearFiles" />
        </div>
      </div>
      <div
        v-if="fileList.length > 0 && !autoUpload && !isUploading && fileList[0].status === 'pending'"
        class="p-fileupload-logo-actions"
      >
        <Button icon="pi pi-upload" class="p-button-rounded p-button-success" :disabled="!canUpload" @click="upload" />
        <Button icon="pi pi-times" class="p-button-rounded p-button-danger" @click="clearFiles" />
      </div>
      <ProgressBar
        v-if="isUploading && fileList.filter((item: FileUpload.FileListItem) => item.status === 'uploading').length > 1"
        :value="totalProgress"
      />
    </div>

    <!-- Standard Mode -->
    <div v-else class="p-fileupload-content">
      <!-- Drop Zone -->
      <div
        class="p-fileupload-dropzone" :class="{ 'p-fileupload-dragover': dragOver }" @click="selectFiles"
        @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop"
      >
        <slot name="upload-area">
          <div class="p-fileupload-upload">
            <slot name="upload-icon">
              <i class="pi pi-upload p-fileupload-icon" />
            </slot>
            <slot name="upload-text">
              <span v-if="!dragOver">Drop files here or click to upload</span>
              <span v-else>Release to upload</span>
            </slot>
          </div>
        </slot>
      </div>

      <!-- File List -->
      <div v-if="showFileList && fileList.length > 0" class="p-fileupload-files" :style="fileListStyles">
        <div
          v-for="item in fileList" :key="item.id" class="p-fileupload-row" :class="{
            'p-fileupload-row-uploading': item.status === 'uploading',
            'p-fileupload-row-success': item.status === 'success',
            'p-fileupload-row-error': item.status === 'error',
            'p-fileupload-row-warning': item.warning,
          }"
        >
          <div class="p-fileupload-row-content">
            <div class="p-fileupload-file">
              <slot
                name="file-item" :file="item.file" :preview="item.preview" :status="item.status"
                :warning="item.warning"
              >
                <div class="p-fileupload-file-thumbnail">
                  <img v-if="item.preview" :src="item.preview" :alt="item.file.name" class="p-fileupload-file-preview">
                  <i v-else class="p-fileupload-file-icon" :class="[getFileIcon(item.file.type)]" />
                </div>
                <div class="p-fileupload-file-details">
                  <div class="p-fileupload-file-name">
                    {{ item.file.name }}
                  </div>
                  <div class="p-fileupload-file-size">
                    {{ getReadableFileSize(item.file.size) }}
                  </div>
                  <div v-if="item.status === 'error'" class="p-fileupload-file-error">
                    {{ item.error }}
                  </div>
                  <div v-if="item.warning" class="p-fileupload-file-warning">
                    <i class="pi pi-exclamation-triangle" style="margin-right: 4px;" />
                    {{ item.warning }}
                  </div>
                </div>
              </slot>
            </div>

            <div class="p-fileupload-file-actions">
              <Button
                v-if="item.status === 'pending'" icon="pi pi-upload"
                class="p-button-outlined p-button-rounded p-button-success"
                @click="() => { if (canUpload) uploadFile(item) }"
              />
              <Button
                icon="pi pi-times" class="p-button-outlined p-button-rounded p-button-danger"
                @click="() => removeFile(item.id)"
              />
            </div>
          </div>

          <div v-if="item.status === 'uploading'" class="p-fileupload-file-progress">
            <ProgressBar :value="item.progress" />
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div v-if="!autoUpload && !isUploading && hasFiles" class="p-fileupload-actions">
        <Button icon="pi pi-upload" label="Upload All" :disabled="!canUpload" @click="upload" />
        <Button icon="pi pi-ban" label="Clear" class="p-button-outlined p-button-danger" @click="clearFiles" />
      </div>

      <div
        v-if="isUploading && fileList.filter((item: FileUpload.FileListItem) => item.status === 'uploading').length > 1"
        class="p-fileupload-progress"
      >
        <div class="p-fileupload-progress-label">
          {{ totalProgress }}%
        </div>
        <ProgressBar :value="totalProgress" />
        <Button
          icon="pi pi-times" class="p-button-rounded p-button-danger p-button-outlined p-fileupload-cancel"
          @click="abort"
        />
      </div>
    </div>

    <!-- Hidden File Input -->
    <input
      ref="fileInput" type="file" class="p-hidden" :accept="accept" :multiple="multiple" :disabled="disabled"
      @change="handleFileSelect"
    >
  </div>
</template>

<style scoped>
.p-fileupload {
  width: 100%;
}

.p-fileupload-content {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.p-fileupload-dropzone {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  border: 2px dashed var(--surface-border, #ddd);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.p-fileupload-dragover {
  border-color: var(--primary-color, #2196F3);
  background-color: var(--surface-hover, #f1f1f1);
}

.p-fileupload-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.p-fileupload-icon {
  font-size: 2rem;
  color: var(--text-color-secondary, #6c757d);
}

.p-fileupload-files {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow: auto;
}

.p-fileupload-row {
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  border: 1px solid var(--surface-border, #ddd);
  border-radius: 0.5rem;
  background-color: var(--surface-card, #fff);
  overflow: hidden;
}

.p-fileupload-row-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.p-fileupload-file {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.p-fileupload-file-thumbnail {
  width: 48px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-ground, #f8f9fa);
  border-radius: 4px;
}

.p-fileupload-file-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.p-fileupload-file-icon {
  font-size: 1.5rem;
  color: var(--text-color-secondary, #6c757d);
}

.p-fileupload-file-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.p-fileupload-file-name {
  font-weight: 600;
  word-break: break-all;
}

.p-fileupload-file-size {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6c757d);
}

.p-fileupload-file-error {
  font-size: 0.75rem;
  color: var(--red-500, #f44336);
}

.p-fileupload-file-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
  /* Ensure actions stay on the right */
}

.p-fileupload-file-progress {
  margin-top: 0.5rem;
  width: 100%;
}

.p-fileupload-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.p-fileupload-progress {
  position: relative;
  width: 100%;
  margin-top: 0.5rem;
}

.p-fileupload-progress-label {
  position: absolute;
  top: -1.25rem;
  right: 0;
  font-size: 0.75rem;
}

.p-fileupload-cancel {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

/* 确保所有进度条有一致的样式 */
:deep(.p-progressbar) {
  height: 0.5rem;
  background: var(--surface-300, #e0e0e0);
  border-radius: 0.25rem;
  overflow: hidden;
}

:deep(.p-progressbar-value) {
  background: var(--primary-color, #2196F3);
  border-radius: 0.25rem;
  transition: width 0.2s ease;
}

.p-fileupload-file-progress :deep(.p-progressbar) {
  margin: 0;
  height: 0.5rem;
}

/* 红色边框区域的样式改进 */
.p-fileupload-row-uploading {
  background-color: var(--blue-50, #e3f2fd);
  border-color: var(--blue-200, #90CAF9);
}

.p-fileupload-row-success {
  background-color: var(--green-50, #e8f5e9);
  border-color: var(--green-200, #A5D6A7);
}

.p-fileupload-row-error {
  background-color: var(--red-50, #ffebee);
  border-color: var(--red-200, #EF9A9A);
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

.p-fileupload-dragover {
  animation: pulse 1s infinite;
}

.p-fileupload-errors {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.p-hidden {
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
  opacity: 0;
}

/* 头像模式样式 */
.p-fileupload-avatar-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.p-fileupload-avatar-dropzone {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.p-fileupload-avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--surface-ground, #f8f9fa);
  color: var(--text-color-secondary, #6c757d);
  padding: 1rem;
  box-sizing: border-box;
  text-align: center;
}

.p-fileupload-avatar-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.p-fileupload-text {
  font-size: 0.75rem;
  line-height: 1.2;
  max-width: 100px;
  margin: 0 auto;
  word-break: break-word;
}

.p-fileupload-avatar-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.p-fileupload-avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.p-fileupload-avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.p-fileupload-avatar-dropzone:hover .p-fileupload-avatar-overlay {
  opacity: 1;
}

.p-fileupload-avatar-overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: white;
}

.p-fileupload-avatar-actions {
  display: flex;
  gap: 0.5rem;
}

/* 图库模式样式 */
.p-fileupload-gallery-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.p-fileupload-gallery-dropzone {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  border: 2px dashed var(--surface-border, #ddd);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--surface-ground, #f8f9fa);
}

.p-fileupload-gallery-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color-secondary, #6c757d);
}

.p-fileupload-gallery-icon {
  font-size: 2rem;
}

.p-fileupload-gallery-files {
  overflow: auto;
}

.p-fileupload-gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.p-fileupload-gallery-item {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  aspect-ratio: 1;
}

.p-fileupload-gallery-item-preview {
  width: 100%;
  height: 100%;
  background-color: var(--surface-ground, #f8f9fa);
  display: flex;
  align-items: center;
  justify-content: center;
}

.p-fileupload-gallery-item-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.p-fileupload-gallery-item-preview i {
  font-size: 2rem;
  color: var(--text-color-secondary, #6c757d);
}

.p-fileupload-gallery-item-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.p-fileupload-gallery-item-uploading .p-fileupload-gallery-item-overlay {
  opacity: 1;
}

.p-fileupload-gallery-item:hover .p-fileupload-gallery-item-overlay {
  opacity: 1;
}

.p-fileupload-gallery-item-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.p-fileupload-gallery-item-name {
  font-size: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.p-fileupload-gallery-item-size {
  font-size: 0.7rem;
  opacity: 0.8;
}

.p-fileupload-gallery-item-actions {
  display: flex;
  justify-content: space-between;
}

.p-fileupload-gallery-item-progress {
  height: 0.5rem;
  margin-top: 0.25rem;
  overflow: hidden;
  border-radius: 0.25rem;
}

.p-fileupload-gallery-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .p-fileupload-gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .p-fileupload-row-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .p-fileupload-file {
    margin-bottom: 0.5rem;
    width: 100%;
  }

  .p-fileupload-file-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

.p-fileupload-dragover {
  animation: pulse 1s infinite;
}

.p-fileupload-gallery-item-uploading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(33, 150, 243, 0.3));
  animation: pulse 1.5s infinite;
}

.p-fileupload-gallery-item-success::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.3));
}

.p-fileupload-gallery-item-error::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(244, 67, 54, 0.3));
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {

  .p-fileupload-dropzone,
  .p-fileupload-gallery-dropzone {
    border-color: rgba(255, 255, 255, 0.2);
  }

  .p-fileupload-dragover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .p-fileupload-row-uploading {
    background-color: rgba(33, 150, 243, 0.2);
  }

  .p-fileupload-row-success {
    background-color: rgba(76, 175, 80, 0.2);
  }

  .p-fileupload-row-error {
    background-color: rgba(244, 67, 54, 0.2);
  }
}

/* Add warning styling */
.p-fileupload-row-warning {
  background-color: var(--yellow-50, #fffde7);
  border-color: var(--yellow-200, #FFE082);
}

.p-fileupload-file-warning {
  font-size: 0.75rem;
  color: var(--yellow-600, #FFA000);
  display: flex;
  align-items: center;
}

/* Logo Mode Styles */
.p-fileupload-logo-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.p-fileupload-logo-dropzone {
  position: relative;
  width: 350px;
  height: 200px;
  border: 1px solid #545454;
  border-radius: 7px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--surface-ground, #f8f9fa);
  display: flex;
  align-items: center;
  justify-content: center;
}

.p-fileupload-logo-placeholder {
  /* position: relative; */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color-secondary, #6c757d);
}

.p-fileupload-logo-icon {
  font-size: 2rem;
}

.p-fileupload-logo-preview {
  /* position: absolute; */
  /* top: 0; */
  /* left: 0; */
  /* width: 100%; */
  height: 100%;
  padding: 1rem;
}

.p-fileupload-logo-preview img {
  /* width: 100%; */
  height: 100%;
  object-fit: contain;
}

.p-fileupload-logo-actions {
  display: flex;
  gap: 0.5rem;
}

.p-fileupload-logo-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.p-fileupload-logo-progress-text {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6c757d);
}

.p-fileupload-avatar-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.p-fileupload-avatar-progress-text {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6c757d);
}
</style>
