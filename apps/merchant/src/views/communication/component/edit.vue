<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { getTemplate } from '@/services/api/notification'
import customerUpload from './customerUpload.vue'

const props = defineProps({
  mode: {
    type: String,
    default: 'template',
  },
  template: {
    type: Array,
    default: () => [],
  },
  isShowTip: {
    type: Boolean,
    default: true,
    required: false,
  },
})

const emits = defineEmits(['update:logo', 'update:selectedColor', 'update:changeContent', 'update:changeTemplateType'])

const logoModel = defineModel<FileUpload.UploadFileItem[]>('logo')

const recentColors = ref([
  { name: 'orange', hex: '#fe4c1c' },
  { name: 'blue', hex: '#09deff' },
  { name: 'green', hex: '#e1ffa9' },
  { name: 'pink', hex: '#ffe3e8' },
  { name: 'dark', hex: '#181349' },
  { name: 'light', hex: '#ffffff' },
])
const colorValue = ref('#ef5129')

const templateValue = ref()
const selectColor = (color: string) => {
  const selectedColor = color.slice(0, 1) === '#' ? color : `#${color}`
  colorValue.value = color.slice(0, 1) === '#' ? color : `#${color}`
  emits('update:selectedColor', selectedColor)
}

const messageTemplate = ref()
const handleAccordionOpen = async (event: { index: number }) => {
  emits('update:changeContent', findTemplate(event.index))
}
const findTemplate = (index: number) => {
  if (templateValue.value) {
    return templateValue.value.find((item: Api.NotificationListTemplate) => item.notification_type === index).content
  }
  return ''
}

const cancelMessage = () => {
  emits('update:changeContent', '')
}
const saveTemplate = (type: number) => {
  emits('update:changeTemplateType', type)
}
onMounted(() => {
  Promise.all([
    getTemplate().then((res) => {
      templateValue.value = res.data.data
    }),
  ])
})
</script>

<template>
  <div class="edit">
    <div class="multiple">
      <Accordion multiple :value="['0']" expand-icon="pi pi-sort-down-fill" collapse-icon="pi pi-sort-up-fill">
        <AccordionPanel value="0">
          <AccordionHeader>
            <span class="flex items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Logo and Colors</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="flex justify-start gap-6">
              <div>
                <label class="block text-900 font-medium mb-2">Add Your Logo</label>
                <div class="">
                  <div class="mb-4 flex justify-center">
                    <div class="p-fileupload-content">
                      <!-- Drop Zone -->
                      <customerUpload
                        v-model:model-value="logoModel" mode="logo" :multiple="false" :max-files="1"
                        accept="image/*"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <label class="block mb-2">Pick Your Colors</label>
                <div class="color-picker-section relative">
                  <div class="absolute -top-4 left-2 w-[96%]">
                    <img src="@/assets/merchant/color.png" alt="" style="height:200px;width: 100%;">
                  </div>
                  <div class="w-[90%] absolute bottom-2 left-4">
                    <div class="flex justify-between items-end">
                      <div>
                        <span> Recently Used </span>
                        <div class="recent-colors flex gap-2 mt-2">
                          <div
                            v-for="(color, index) in recentColors" :key="index" class="recent-color-item"
                            :style="{ backgroundColor: color.hex }" @click="selectColor(color.hex)"
                          />
                        </div>
                      </div>
                      <div>
                        <ColorPicker v-model="colorValue" @update:model-value="selectColor($event)" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel value="1">
          <AccordionHeader>
            <span class="flex items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">URL</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="flex justify-between gap-10">
              <div class="address w-1/2">
                <label class="block text-900 font-medium mb-2">URL Address</label>
                <InputText disabled placeholder="https://" class="w-full" />
              </div>
              <div class="redirection w-1/2">
                <label class="block text-900 font-medium mb-2">URD redirection after success</label>
                <InputText disabled class="w-full" />
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
      </Accordion>
    </div>
    <div class="radio mt-6">
      <Accordion
        v-if="props.mode === 'template'" :value="messageTemplate" expand-icon="pi pi-sort-down-fill"
        collapse-icon="pi pi-sort-up-fill" @tab-open="handleAccordionOpen"
      >
        <AccordionPanel :value="6">
          <AccordionHeader>
            <span class="flex items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Invite a customer</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(6)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <button class="cancel-btn" @click="cancelMessage">
                  CANCEL
                </button>
                <button class="save-btn" @click="saveTemplate(6)">
                  SAVE CHANGE
                </button>
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="4">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Confirmation of subscription</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(4)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <button class="cancel-btn" @click="cancelMessage">
                  CANCEL
                </button>
                <button class="save-btn" @click="saveTemplate(4)">
                  SAVE CHANGE
                </button>
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="1">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Successful payment</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(1)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <button class="cancel-btn" @click="cancelMessage">
                  CANCEL
                </button>
                <button class="save-btn" @click="saveTemplate(1)">
                  SAVE CHANGE
                </button>
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="2">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Upcoming payment</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(2)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <button class="cancel-btn" @click="cancelMessage">
                  CANCEL
                </button>
                <button class="save-btn" @click="saveTemplate(2)">
                  SAVE CHANGE
                </button>
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="8">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Subscription update</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(8)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <button class="cancel-btn" @click="cancelMessage">
                  CANCEL
                </button>
                <button class="save-btn" @click="saveTemplate(8)">
                  SAVE CHANGE
                </button>
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="3">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Fail/Retry payment</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(3)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <button class="cancel-btn" @click="cancelMessage">
                  CANCEL
                </button>
                <button class="save-btn" @click="saveTemplate(3)">
                  SAVE CHANGE
                </button>
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="9">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Subscription cancel</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->
              <Card>
                <template #content>
                  <p v-html="findTemplate(9)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <button class="cancel-btn" @click="cancelMessage">
                  CANCEL
                </button>
                <button class="save-btn" @click="saveTemplate(9)">
                  SAVE CHANGE
                </button>
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
        <AccordionPanel :value="5">
          <AccordionHeader>
            <span class="flex  items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Payment method update</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <!-- <Textarea v-model="value" rows="5" cols="96" disabled style="resize: none" /> -->

              <Card>
                <template #content>
                  <p v-html="findTemplate(5)" />
                </template>
              </Card>
              <div class="button-group flex justify-end gap-2 mt-4">
                <button class="cancel-btn" @click="cancelMessage">
                  CANCEL
                </button>
                <button class="save-btn" @click="saveTemplate(5)">
                  SAVE CHANGE
                </button>
              </div>
            </div>
          </AccordionContent>
        </AccordionPanel>
      </Accordion>
      <Accordion v-else :value="0" expand-icon="pi pi-sort-down-fill" collapse-icon="pi pi-sort-up-fill">
        <AccordionPanel :value="0">
          <AccordionHeader>
            <span class="flex items-center gap-2 w-full">
              <span class="font-bold text-xl whitespace-nowrap">Message</span>
            </span>
          </AccordionHeader>
          <AccordionContent>
            <div class="p-2">
              <div class="flex w-1/2 items-center mb-2">
                <h3 class=" font-semibold ">
                  Custom Message
                </h3>
                <Tag value="Optional" class="  ml-4" />
              </div>
              <Card>
                <template #content>
                  <p v-html="findTemplate(6)" />
                </template>
              </Card>
              <div v-if="props.isShowTip" class="text-[#b1b1b1]  ml-4 mt-2">
                If you don't add a custom message, we'll default to GoCardless' messaging.
              </div>
              <!-- <div class="button-group flex justify-end gap-2 mt-4">
                <button class="cancel-btn">
                  CANCEL
                </button>
                <button class="save-btn">
                  SAVE CHANGE
                </button>
              </div> -->
            </div>
          </AccordionContent>
        </AccordionPanel>
      </Accordion>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.edit {
  --p-accordion-header-toggle-icon-color: #fe4c1c;
  --p-accordion-header-toggle-icon-active-color: #fe4c1c;
}

.cancel-btn,
.save-btn {
  padding: 10px 30px;
  background-color: var(--color-white-100);
  border: none;
  color: #181349;
  font-weight: 700;
  border-radius: 7px;
  font-size: 12px;
  cursor: pointer;
}

.cancel-btn:hover {
  background-color: #dcdce4;
}

.save-btn {
  background-color: #fe4c1c;
  color: #fff;
}

.save-btn:hover {
  background-color: #e94618;
}

.color-picker-section {
  width: 350px;
  height: 200px;
  border: 1px solid #545454;
  border-radius: 7px;
  padding: 1rem;
}

.recent-color-item {
  border: 1px solid #181349;
  border-radius: 7px;
  width: 21px;
  height: 21px;
  cursor: pointer;
}
</style>
