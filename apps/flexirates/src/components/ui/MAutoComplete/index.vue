<script setup lang="ts">
import type { Slots } from 'vue'
import { useSlots } from 'vue'

// Get slots with explicit type annotation
const slots: Slots = useSlots()
const slotNames: string[] = Object.keys(slots)
</script>

<template>
  <AutoComplete v-bind="$attrs">
    <template v-for="name in slotNames" :key="name" #[name]="slotData">
      <slot :name="name" v-bind="slotData || {}" />
    </template>
  </AutoComplete>
</template>
