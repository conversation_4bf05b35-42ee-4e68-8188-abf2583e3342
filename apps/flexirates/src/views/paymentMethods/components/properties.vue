<script setup lang="ts">
import { MDialog } from '@ui'
import dayjs from 'dayjs'
import Checkbox from 'primevue/checkbox'
import { computed, nextTick, ref } from 'vue'
import { useRouter } from 'vue-router'
import TermsAndConditions from '@/components/termsAndConditions/index.vue'
import { paymentMethod as paymentMethodApi } from '@/services/flexirates'
import { getBankTypeImageUrl } from '@/utils/bank'

const props = withDefaults(defineProps<{
  property?: Api.PaymentMethodInfo
  loading?: boolean
}>(), {
  property: () => ({
    customer_id: '',
    id: 0,
    nickname: '',
    payment_methods: [],
    postcode: '',
    property_number: '',
    street_address: '',
    suburb: '',
  }),
  loading: false,
})

const emits = defineEmits<{
  (e: 'editPaymentMethod', id: number): void
  (e: 'refresh'): void
  (e: 'addCard', propertyId: number): void
}>()

const popRef = ref()

const router = useRouter()

const updataWeightLoading = ref(false)

const showConfirmDialog = ref(false)

const showTerms = ref(false)

const editPaymentMethodSendData = ref({
  property_id: 0,
  id: 0,
  levelPaymentMethod: true,
})

const columns = computed(() => {
  const isFindBsb = props.property?.payment_methods?.some(item => item?.bsb)
  const isFindAccount = props.property?.payment_methods?.some(item => item?.account_no)
  return [
    {
      field: '',
      header: '',
      style: { width: '70px' },
      template: 'cardIcon',
    },
    isFindBsb && {
      field: 'bsb',
      header: 'BSB',
      style: { width: '200px' },
      template: 'bsb',
    },
    isFindAccount && {
      field: 'account_no',
      header: 'Account Number',
      style: { width: '200px' },
      template: 'account_no',
    },
    {
      field: 'expiry_date',
      header: 'Expiry Date',
      style: { width: '10%' },
      template: 'expiry_date',
    },
    {
      field: 'status',
      header: 'Status',
      style: { width: '10%' },
      template: 'status',
    },
    {
      field: 'last_payment',
      header: 'Last Payment',
      template: 'last_payment',
    },
    {
      field: 'switch',
      header: '',
      style: { width: '15%' },
      template: 'switch',
    },
    {
      field: 'actions',
      header: '',
      template: 'actions',
      style: { width: '10%' },
    },
  ].filter(Boolean) as TableColumnItem[]
})

const switchPaymentWeight = async (propertyId: number, id: number) => {
  editPaymentMethodSendData.value.id = id
  editPaymentMethodSendData.value.property_id = propertyId
  editPaymentMethodSendData.value.levelPaymentMethod = false
  showConfirmDialog.value = true
}

const confirmPaymentMethod = async () => {
  if (!editPaymentMethodSendData.value.levelPaymentMethod) {
    window.$toast.add({
      severity: 'error',
      summary: 'Error',
      detail: 'Please read and agree to the Terms and Conditions',
    })
    return
  }

  try {
    updataWeightLoading.value = true
    const res = await paymentMethodApi.updateWeight({ property_id: editPaymentMethodSendData.value.property_id, id: editPaymentMethodSendData.value.id })
    if (res.code === 0) {
      props.property.payment_methods = props.property?.payment_methods?.map((item) => {
        if (item.id === editPaymentMethodSendData.value.id) {
          item.weight = 1
        }
        else {
          item.weight = 2
        }
        return item
      })
      window.$toast.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Update Weight successful',
      })
      showConfirmDialog.value = false
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    updataWeightLoading.value = false
  }
}

const toCardDetail = (id: number) => {
  router.push({ name: 'paymentMethodsCardDetail', params: { id } })
}

const toBankDetail = (id: number) => {
  router.push({ name: 'paymentMethodsBankDetail', params: { id } })
}

const addCard = () => {
  emits('addCard', props.property.id)
}

const showTips = (event: Event, _: number) => {
  popRef.value.hide()
  nextTick(() => {
    popRef.value.toggle(event)
  })
}

const formatBankAccount = (account: string) => {
  return `**** ${String(account)?.slice(-4)}`
}

const getCardTypeStyle = (cardType: number) => {
  switch (cardType) {
    case 1:
      return {
        width: '40px',
        maxWidth: '40px',
        height: '28px',
        borderRadius: '4px',
      }
    case 2:
      return {
        width: '28px',
        maxWidth: '28px',
        height: '28px',
        borderRadius: '4px',
      }
    case 3:
      return {
        width: '42px',
        maxWidth: '42px',
        height: '28px',
        borderRadius: '4px',
      }
    case 4:
      return {
        width: '34px',
        maxWidth: '34px',
        height: '32px',
        borderRadius: '4px',
      }
    case 5:
      return {
        width: '34px',
        maxWidth: '34px',
        height: '28px',
        borderRadius: '4px',
      }
    case 0:
    case 6:
      return {
        width: '28px',
        maxWidth: '28px',
        height: '28px',
      }
  }

  return {
    width: '24px',
    height: '24px',
    borderRadius: '4px',
  }
}
</script>

<template>
  <div class="p-8 bg-white rounded-2xl">
    <div class="flex justify-between items-center">
      <div class="flex flex-col gap-1">
        <div class="text-2xl font-bold border-b-2 border-gray-500 pb-1 w-fit">
          Property Nickname: <span class="text-gray-500 font-normal">
            {{ props.property.nickname }}
          </span>
        </div>
        <div class="text-sm text-gray-500 mt-2">
          <span class="font-bold text-blue-500">
            Property Number:
          </span> <span class="text-gray-500 font-normal">
            {{ props.property.property_number }}
          </span>
        </div>
        <div class="text-sm text-gray-500">
          <span class="font-bold text-blue-500">
            Address:
          </span> <span class="text-gray-500 font-normal">
            {{ props.property.street_address }}
          </span>
        </div>
      </div>
      <div class="flex flex-col gap-2">
        <Button
          label="EDIT PAYMENT METHOD" severity="warn" class="!px-6"
          @click="emits('editPaymentMethod', props.property.id)"
        />
      </div>
    </div>
    <div class="mt-4">
      <BaseDataTable
        :columns="columns" :value="props.property?.payment_methods || []" :loading="false"
        :paginator="false" data-key="id" :show-search-bar="false" :show-multiple-column="false"
        :scrollable="true" table-style="min-width: 15rem"
      >
        <template #bsb="{ data }">
          <div class="line-clamp-1 w-full">
            {{ data.bsb ? formatBankAccount(data.bsb) : '' }}
          </div>
        </template>
        <template #account_no="{ data }">
          <div class="line-clamp-1 w-full">
            {{ data.account_no ? formatBankAccount(data.account_no) : '' }}
          </div>
        </template>
        <template #cardIcon="{ data }">
          <div class="w-full flex justify-start items-center">
            <img :style="getCardTypeStyle(data.credit_brand as number)" :src="getBankTypeImageUrl(data.credit_brand as number)" alt="icon">
          </div>
        </template>
        <template #status="{ data }">
          <span
            class="font-semibold"
            :class="{ 'text-[#eb001b]': data.status === 3, 'text-[#39b54a]': data.status === 1 }"
          >{{ data?.status_desc }}</span>
        </template>
        <template #expiry_date="{ data }">
          <div class="line-clamp-1 w-full">
            {{ data.expiration_month }}
            <span v-if="data.expiration_year" class="mx-1">/</span>
            {{ data.expiration_year }}
          </div>
        </template>
        <template #last_payment="{ data }">
          {{ data.last_payment_date ? dayjs(data.last_payment_date).format('DD MMM YY') : '' }}
        </template>
        <template #switch="{ data }">
          <div class="w-full flex justify-start items-center">
            <span v-if="data?.weight === 1" class="font-semibold text-[#39b54a] pl-3">Primary</span>
            <Button
              v-else label="Switch as Primary" variant="text" :loading="updataWeightLoading"
              style="color: #0073cf;" @click="switchPaymentWeight(props.property.id, data.id)"
            />
            <Button
              v-show="data.is_expiring_soon" icon="pi pi-info-circle" variant="text" rounded
              style="font-size: 1.5rem;color: #eb001b;" @click="showTips($event, data.id)"
            />
          </div>
        </template>
        <template #actions="{ data }">
          <div class="w-full flex justify-end">
            <Button
              label="View Details" class="underline" variant="text" @click="
                data.bsb ? toBankDetail(data.id) : toCardDetail(data.id)
              "
            />
          </div>
        </template>
        <template #empty>
          <div class="text-center p-4">
            <p>No card records found</p>
            <Button label="Add Card" icon="pi pi-plus" @click="addCard" />
          </div>
        </template>
      </BaseDataTable>
      <div v-if="(props?.property?.payment_methods?.length || 0) <= 1" class="flex border-t border-gray-200 pt-4">
        <Button label="Add Secondary Payment Method" text icon="pi pi-plus" @click="addCard" />
      </div>
    </div>

    <div class="pop">
      <CustomPop ref="popRef" title="Reminder">
        <template #content>
          <div class="w-60 mt-4">
            <p class="mb-4">
              Your Card is expiring with in the next 3 months.
            </p>
            <p>Please add a new payment method to avoid any penalties.</p>
          </div>
        </template>
      </CustomPop>
    </div>

    <!-- confirm payment method -->
    <MDialog v-model="showConfirmDialog" header="Confirm Payment Method Change">
      <div class="max-w-150 mt-4 text-gray-500">
        <p class="mb-4">
          You're about to change your payment method—such as switching from a bank account to a card, or reassigning your primary and secondary payment preferences.
        </p>
        <p class="mb-4">
          Please note: This action may require you to set up a new Direct Debit Request (DDR) to authorise future payments from your selected method.
        </p>
        <div class="flex gap-2">
          <p class="mb-4">
            <Checkbox v-model="editPaymentMethodSendData.levelPaymentMethod" input-id="binary" binary />
          </p>
          <div class="flex flex-col">
            <label for="binary">
              By ticking, you are confirming that you have read, understood and agree to the
            </label>
            <span class="underline font-bold cursor-pointer w-fit" @click.stop="showTerms = true">
              Terms and Conditions.
            </span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-4">
          <Button label="Cancel" outlined :loading="updataWeightLoading" @click="showConfirmDialog = false" />
          <Button label="Confirm" severity="warn" :loading="updataWeightLoading" @click="confirmPaymentMethod" />
        </div>
      </template>
    </MDialog>
    <TermsAndConditions v-model="showTerms" />
  </div>
</template>
